import { ConfigModule as NestConfigModule } from '@nestjs/config'
import { ConfigService } from './config.service'
import { Global, Module } from '@nestjs/common'
import { CONFIG_MODULES } from './modules'

@Global()
@Module({
  exports: [ConfigService],
  imports: [
    NestConfigModule.forRoot({
      load: CONFIG_MODULES,
      envFilePath: [
        `config/${process.env.NODE_ENV}.env.local`,
        `config/${process.env.NODE_ENV}.env`,
        '../../.env',
        '.env',
      ],
      // `isGlobal` must be set until all providers import the new config module
      isGlobal: true,
    }),
  ],
  providers: [ConfigService],
})
export class ConfigModule {}
